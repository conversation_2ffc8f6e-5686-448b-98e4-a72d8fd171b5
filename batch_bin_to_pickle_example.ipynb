{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 改善版：競馬データ一括処理Notebook\n", "\n", "このNotebookは、最新の統合プロセッサクラスを使用して、\n", "複数年分のbinファイルをDataFrameに変換し、レース情報と馬情報を統合処理します。\n", "\n", "## 主な改善点\n", "- 最新の`RaceHorseTargetedProcessor`を使用\n", "- エラーハンドリングとログ機能の強化\n", "- 並列処理の最適化\n", "- 進捗表示の改善\n", "- 設定の外部化\n", "- 結果の検証機能"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "import logging\n", "from pathlib import Path\n", "from typing import Dict, List, Optional, Any\n", "import json\n", "from datetime import datetime\n", "\n", "# 最新のプロセッサクラスをインポート\n", "from module.race_batch_processor import process_race_bin_to_pickle_batch\n", "from module.race_horse_targeted_processor import RaceHorseTargetedProcessor\n", "from module.race_data_processor import RaceProcessor\n", "\n", "import pandas as pd\n", "import numpy as np\n", "from tqdm.notebook import tqdm"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 設定とログ初期化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ログ設定\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',\n", "    handlers=[\n", "        logging.StreamHandler(),\n", "        logging.FileHandler(f'batch_processing_{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.log')\n", "    ]\n", ")\n", "logger = logging.getLogger(__name__)\n", "\n", "# 処理設定\n", "CONFIG = {\n", "    # 処理対象年度（必要に応じて変更）\n", "    'target_years': ['2024'],  # 例: ['2022', '2023', '2024']\n", "    \n", "    # ディレクトリ設定\n", "    'base_dir': Path.cwd(),\n", "    'data_dir': Path.cwd() / 'data' / 'html' / 'race' / 'race_by_year',\n", "    'output_dir': Path('output'),\n", "    \n", "    # 並列処理設定\n", "    'parallel': True,\n", "    'max_workers': 4,\n", "    \n", "    # 処理オプション\n", "    'include_race_info': True,\n", "    'include_race_results': True,\n", "    'include_horse_info': True,\n", "    'include_horse_results': True,\n", "    \n", "    # 保存設定\n", "    'save_intermediate': True,  # 中間結果を保存するか\n", "    'validate_results': True,   # 結果を検証するか\n", "}\n", "\n", "# 出力ディレクトリの作成\n", "CONFIG['output_dir'].mkdir(exist_ok=True)\n", "\n", "logger.info(f\"処理設定: {json.dumps({k: str(v) for k, v in CONFIG.items()}, indent=2, ensure_ascii=False)}\")\n", "print(f\"処理対象年: {CONFIG['target_years']}\")\n", "print(f\"データディレクトリ: {CONFIG['data_dir']}\")\n", "print(f\"出力ディレクトリ: {CONFIG['output_dir']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 方法1: 最新のバッチプロセッサを使用したレースデータ処理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def validate_data_quality(df: pd.DataFrame, data_type: str) -> Dict[str, Any]:\n", "    \"\"\"データ品質を検証する関数\"\"\"\n", "    validation_result = {\n", "        'data_type': data_type,\n", "        'total_rows': len(df),\n", "        'total_columns': len(df.columns),\n", "        'null_counts': df.isnull().sum().to_dict(),\n", "        'duplicate_rows': df.duplicated().sum(),\n", "        'memory_usage_mb': df.memory_usage(deep=True).sum() / 1024 / 1024\n", "    }\n", "    \n", "    # データタイプ固有の検証\n", "    if data_type == 'race_info':\n", "        if 'race_id' in df.columns:\n", "            validation_result['unique_race_ids'] = df['race_id'].nunique()\n", "            validation_result['duplicate_race_ids'] = df['race_id'].duplicated().sum()\n", "    elif data_type == 'race_results':\n", "        if 'horse_id' in df.columns:\n", "            validation_result['unique_horse_ids'] = df['horse_id'].nunique()\n", "        if 'race_id' in df.columns:\n", "            validation_result['unique_race_ids'] = df['race_id'].nunique()\n", "    \n", "    return validation_result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 方法1: 最新のバッチプロセッサを使用\n", "print(\"=== 方法1: 最新のバッチプロセッサを使用したレースデータ処理 ===\")\n", "\n", "race_results = {}\n", "validation_reports = {}\n", "\n", "for year in CONFIG['target_years']:\n", "    print(f\"\\n{year}年のレースデータを処理中...\")\n", "    \n", "    try:\n", "        # バッチプロセッサを使用してレースデータを処理\n", "        year_data = process_race_bin_to_pickle_batch(\n", "            year=year,\n", "            parallel=CONFIG['parallel'],\n", "            max_workers=CONFIG['max_workers']\n", "        )\n", "        \n", "        if year_data:\n", "            race_results[year] = year_data\n", "            \n", "            # データ品質検証\n", "            if CONFIG['validate_results']:\n", "                validation_reports[year] = {}\n", "                for data_type, df in year_data.items():\n", "                    if df is not None and not df.empty:\n", "                        validation_reports[year][data_type] = validate_data_quality(df, data_type)\n", "                        print(f\"  {data_type}: {len(df):,}行, {len(df.columns)}列\")\n", "            \n", "            # 中間結果の保存\n", "            if CONFIG['save_intermediate']:\n", "                for data_type, df in year_data.items():\n", "                    if df is not None and not df.empty:\n", "                        output_path = CONFIG['output_dir'] / f\"{data_type}_{year}.pickle\"\n", "                        df.to_pickle(output_path)\n", "                        print(f\"  保存完了: {output_path}\")\n", "        \n", "        print(f\"{year}年の処理完了\")\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"{year}年の処理中にエラーが発生: {str(e)}\")\n", "        print(f\"エラー: {year}年の処理に失敗しました - {str(e)}\")\n", "        continue\n", "\n", "print(\"\\n=== 方法1の処理完了 ===\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 方法2: 統合プロセッサを使用した処理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 方法2: 統合プロセッサを使用\n", "print(\"=== 方法2: 統合プロセッサを使用した処理 ===\")\n", "\n", "integrated_results = {}\n", "\n", "for year in CONFIG['target_years']:\n", "    print(f\"\\n{year}年のデータを統合プロセッサで処理中...\")\n", "    \n", "    try:\n", "        # 統合プロセッサのインスタンス作成\n", "        processor = RaceHorseTargetedProcessor(\n", "            base_dir=CONFIG['base_dir'],\n", "            parallel=CONFIG['parallel'],\n", "            max_workers=CONFIG['max_workers']\n", "        )\n", "        \n", "        # 年度別データディレクトリ\n", "        year_dir = CONFIG['data_dir'] / year\n", "        \n", "        if not year_dir.exists():\n", "            print(f\"警告: {year_dir} が存在しません\")\n", "            continue\n", "        \n", "        # binファイルのリストを取得\n", "        bin_files = list(year_dir.glob('*.bin'))\n", "        print(f\"  発見されたbinファイル数: {len(bin_files)}\")\n", "        \n", "        if not bin_files:\n", "            print(f\"  {year}年のbinファイルが見つかりません\")\n", "            continue\n", "        \n", "        # 統合処理の実行\n", "        year_results = processor.process_multiple_files(\n", "            file_paths=bin_files,\n", "            include_race_info=CONFIG['include_race_info'],\n", "            include_race_results=CONFIG['include_race_results'],\n", "            include_horse_info=CONFIG['include_horse_info'],\n", "            include_horse_results=CONFIG['include_horse_results']\n", "        )\n", "        \n", "        if year_results:\n", "            integrated_results[year] = year_results\n", "            \n", "            # 結果の表示\n", "            for data_type, df in year_results.items():\n", "                if df is not None and not df.empty:\n", "                    print(f\"  {data_type}: {len(df):,}行, {len(df.columns)}列\")\n", "                    \n", "                    # 保存\n", "                    if CONFIG['save_intermediate']:\n", "                        output_path = CONFIG['output_dir'] / f\"{data_type}_{year}_integrated.pickle\"\n", "                        df.to_pickle(output_path)\n", "                        print(f\"    保存完了: {output_path}\")\n", "        \n", "        print(f\"{year}年の統合処理完了\")\n", "        \n", "    except Exception as e:\n", "        logger.error(f\"{year}年の統合処理中にエラーが発生: {str(e)}\")\n", "        print(f\"エラー: {year}年の統合処理に失敗しました - {str(e)}\")\n", "        continue\n", "\n", "print(\"\\n=== 方法2の処理完了 ===\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 結果の比較と検証"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 結果の比較と検証\n", "print(\"=== 処理結果の比較と検証 ===\")\n", "\n", "for year in CONFIG['target_years']:\n", "    print(f\"\\n{year}年の結果比較:\")\n", "    \n", "    # 方法1の結果\n", "    if year in race_results:\n", "        print(\"  方法1 (バッチプロセッサ):\")\n", "        for data_type, df in race_results[year].items():\n", "            if df is not None and not df.empty:\n", "                print(f\"    {data_type}: {len(df):,}行\")\n", "    \n", "    # 方法2の結果\n", "    if year in integrated_results:\n", "        print(\"  方法2 (統合プロセッサ):\")\n", "        for data_type, df in integrated_results[year].items():\n", "            if df is not None and not df.empty:\n", "                print(f\"    {data_type}: {len(df):,}行\")\n", "    \n", "    # 検証レポートの表示\n", "    if CONFIG['validate_results'] and year in validation_reports:\n", "        print(f\"  データ品質レポート ({year}年):\")\n", "        for data_type, report in validation_reports[year].items():\n", "            print(f\"    {data_type}:\")\n", "            print(f\"      - 重複行数: {report['duplicate_rows']}\")\n", "            print(f\"      - メモリ使用量: {report['memory_usage_mb']:.2f}MB\")\n", "            if 'unique_race_ids' in report:\n", "                print(f\"      - ユニークレースID数: {report['unique_race_ids']}\")\n", "            if 'unique_horse_ids' in report:\n", "                print(f\"      - ユニーク馬ID数: {report['unique_horse_ids']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. データの確認とサンプル表示"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 処理されたデータの確認\n", "print(\"=== 処理されたデータの確認 ===\")\n", "\n", "# 最初の年のデータを確認\n", "if CONFIG['target_years'] and CONFIG['target_years'][0] in race_results:\n", "    first_year = CONFIG['target_years'][0]\n", "    year_data = race_results[first_year]\n", "    \n", "    print(f\"\\n{first_year}年のデータサンプル:\")\n", "    \n", "    for data_type, df in year_data.items():\n", "        if df is not None and not df.empty:\n", "            print(f\"\\n--- {data_type} ---\")\n", "            print(f\"形状: {df.shape}\")\n", "            print(f\"列名: {list(df.columns)}\")\n", "            print(\"\\n先頭5行:\")\n", "            display(df.head())\n", "            \n", "            # 基本統計情報\n", "            if len(df.select_dtypes(include=[np.number]).columns) > 0:\n", "                print(\"\\n数値列の基本統計:\")\n", "                display(df.describe())\n", "else:\n", "    print(\"表示可能なデータがありません\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 最終的な統合と保存"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 複数年のデータを統合\n", "print(\"=== 複数年データの統合 ===\")\n", "\n", "if len(CONFIG['target_years']) > 1:\n", "    combined_data = {}\n", "    \n", "    # データタイプごとに統合\n", "    data_types = ['race_info', 'race_results', 'horse_info', 'horse_results']\n", "    \n", "    for data_type in data_types:\n", "        dfs_to_combine = []\n", "        \n", "        for year in CONFIG['target_years']:\n", "            if year in race_results and data_type in race_results[year]:\n", "                df = race_results[year][data_type]\n", "                if df is not None and not df.empty:\n", "                    dfs_to_combine.append(df)\n", "        \n", "        if dfs_to_combine:\n", "            combined_df = pd.concat(dfs_to_combine, ignore_index=True)\n", "            combined_data[data_type] = combined_df\n", "            \n", "            print(f\"{data_type}: {len(combined_df):,}行 (統合後)\")\n", "            \n", "            # 統合データの保存\n", "            years_str = '_'.join(CONFIG['target_years'])\n", "            output_path = CONFIG['output_dir'] / f\"{data_type}_{years_str}_combined.pickle\"\n", "            combined_df.to_pickle(output_path)\n", "            print(f\"  保存完了: {output_path}\")\n", "    \n", "    print(\"\\n複数年データの統合完了\")\n", "else:\n", "    print(\"単一年のデータのため、統合処理をスキップします\")\n", "\n", "print(\"\\n=== 全ての処理が完了しました ===\")\n", "print(f\"出力ディレクトリ: {CONFIG['output_dir']}\")\n", "print(\"保存されたファイル:\")\n", "for file_path in CONFIG['output_dir'].glob('*.pickle'):\n", "    print(f\"  - {file_path.name}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}