import sys
import os
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
import json
from datetime import datetime

# 最新のプロセッサクラスをインポート
from module.race_batch_processor import process_race_bin_to_pickle_batch
from module.race_horse_targeted_processor import RaceHorseTargetedProcessor
from module.race_data_processor import RaceProcessor

import pandas as pd
import numpy as np
from tqdm.notebook import tqdm

# 処理したい年のリストを指定
years = ['2017', '2018', '2019']  # 必要な年を追加してください
# 単一年の場合: years = ['2024']
# 複数年の場合: years = ['2020', '2021', '2022', '2023', '2024']

# データディレクトリの設定
base_dir = Path.cwd()  # 現在のディレクトリを基準
data_dir = base_dir / 'data' / 'html' / 'race' / 'race_by_year'

# 出力ディレクトリの作成
output_dir = Path('output')
output_dir.mkdir(exist_ok=True)

print(f"処理対象年: {years}")
print(f"データディレクトリ: {data_dir}")
print(f"出力ディレクトリ: {output_dir}")

processor = RaceProcessor()

for year in years:
    print(f'{year}年のbinファイルを処理中...')
    bin_dir = data_dir / year
    bin_files = list(bin_dir.glob('*.bin'))
    race_info_list = []
    race_results_list = []
    for bin_file in bin_files:
        info_df, results_df = processor.parse_race_html(html_path=bin_file)
        if not info_df.empty:
            race_info_list.append(info_df)
        if not results_df.empty:
            race_results_list.append(results_df)
    # 年ごとにまとめてDataFrame化
    race_info_df = pd.concat(race_info_list, ignore_index=True) if race_info_list else pd.DataFrame()
    race_results_df = pd.concat(race_results_list, ignore_index=True) if race_results_list else pd.DataFrame()
    # DataFrameが空でなければ保存
    if not race_info_df.empty:
        race_info_df.to_pickle(output_dir / f'race_info_{year}.pickle')
        print(f'  race_info_{year}.pickle を保存')
    if not race_results_df.empty:
        race_results_df.to_pickle(output_dir / f'race_results_{year}.pickle')
        print(f'  race_results_{year}.pickle を保存')
    print(f'{year}年のpickle保存完了\n')

print('全ての年の処理が完了しました。')

import module.race_batch_processor
# 使用例1: binファイルからpickleファイルへの変換
years = ["2024"]
module.race_batch_processor.process_race_bin_to_pickle_batch(
    years=years,
    bin_base_dir=data_dir,
    output_dir=output_dir,
    parallel=True,
    max_workers=4
)

from module.race_horse_targeted_processor import RaceHorseTargetedProcessor

# プロセッサ作成
processor = RaceHorseTargetedProcessor()

# 2024年のレースから馬IDを抽出し、馬情報を処理
result = processor.process_race_to_horses(
    year="2022",
    include_basic_info=True,
    include_results=True,
    parallel=True,
    max_workers=4,
    save_output=True
)

race_info = pd.read_pickle(output_dir / "race_info_2024.pickle")
race_info

race_results = pd.read_pickle(output_dir / "race_results_2024.pickle")
race_results

horse_info = pd.read_pickle(output_dir / "race_horses_horse_info_2024.pickle")
horse_info

horse_results = pd.read_pickle(output_dir / "race_horses_horse_results_2024.pickle")
horse_results

# Jupyter Notebookのセル

import sys
import os
import pandas as pd
import logging

# 'module'ディレクトリの親ディレクトリをsys.pathに追加 (環境に応じて調整)
# 例: ノートブックが 'notebooks' フォルダにあり、'module' がその一つ上の階層にある場合
# module_parent_dir = os.path.abspath(os.path.join(os.getcwd(), '..'))
# if module_parent_dir not in sys.path:
#     sys.path.append(module_parent_dir)

from module.data_merger import DataMerger
from module.race_data_processor import RaceProcessor
from module.horse_processor import HorseProcessor
from module.constants import LocalPaths # 必要に応じて

# ロギング設定 (任意)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


# Jupyter Notebookのセル

# RaceProcessorのインスタンスを作成
race_processor = RaceProcessor()

# 例: 特定の年のレースデータを処理 (実際のファイルパスや処理に合わせてください)
# process_race_bin_files は (race_info_df, race_results_df) を返しますが、
# DataMerger は race_processor.preprocess_data() を内部で呼び出すことを期待しているようです。
# preprocess_data() が適切なデータを返すように、事前に process_race_bin_files を実行しておくか、
# または RaceProcessor のインスタンス変数 _race_info_df, _race_results_df が設定されるようにします。
logger.info("RaceProcessorでレースデータを処理中...")
# 例: 2023年のデータを処理
# 実際の .bin ファイルの場所に応じて、process_race_bin_files の引数を調整してください。
# LocalPaths.HTML_RACE_DIR が "data/html/race/race_by_year" を指す場合、
# year="2023" とすると "data/html/race/race_by_year/2023/*.bin" を処理します。
race_processor.process_race_bin_files(year="2023", parallel=True, max_workers=4) # 仮の年
# これで race_processor._race_info_df と race_processor._race_results_df が設定されます。
# DataMerger の __init__ は race_processor.preprocess_data() を呼び出します。
logger.info("RaceProcessorのデータ準備完了。")
# Jupyter Notebookのセル
# HorseProcessorのインスタンスを作成
# 例1: Pickleファイルから馬の過去成績と基本情報を読み込む場合
#      実際のファイルパスに置き換えてください。
# horse_results_filepath = "f:/keiba__AI_2025/data/processed/horse_results_2023.pickle" # 仮
# horse_info_filepath = "f:/keiba__AI_2025/data/processed/horse_info_2023.pickle"       # 仮
#
# horse_processor = HorseProcessor(
#     horse_results_filepath=horse_results_filepath,
#     horse_info_filepath=horse_info_filepath
# )

# 例2: HTMLファイルから馬の過去成績と基本情報を処理する場合
#      DataMerger は horse_processor.preprocessed_data (過去成績) と
#      horse_processor から取得できる馬基本情報 (間接的に _horse_info_data) を期待します。
logger.info("HorseProcessorで馬データを準備中...")
horse_processor = HorseProcessor() # データは後からロードまたは処理

# 馬の過去成績をHTMLから取得・前処理 (例: 2023年のデータ)
# get_all_horse_results は前処理済みのDataFrameを返しますが、
# HorseProcessor の preprocessed_data プロパティが更新されるように、
# 結果をインスタンスの _raw_data に設定し、_preprocess を呼び出すか、
# または HorseProcessor のコンストラクタで horse_results_data として渡す必要があります。
# ここでは、コンストラクタでデータを渡すアプローチが DataMerger の期待と整合しやすいです。

# ダミーのDataFrameまたは実際のデータパスを使用してください。
# 以下は、HorseProcessorがHTMLからデータを取得し、内部状態を更新する例です。
# 実際には、HorseProcessorのコンストラクタにファイルパスを渡すか、
# 取得したDataFrameをコンストラクタの horse_results_data や horse_info_data に渡すのが一般的です。

# 例として、特定の馬のIDリストで情報を取得する場合
# horse_ids_for_processor = ['2020100001', '2020100002'] # 仮のID
# results_df = horse_processor.process_horse_results_for_ids(horse_ids=horse_ids_for_processor)
# info_df = horse_processor.process_horse_info_for_ids(horse_ids=horse_ids_for_processor)
# horse_processor = HorseProcessor(horse_results_data=results_df, horse_info_data=info_df)

# より簡単なのは、ファイルパスをコンストラクタに渡すことです。
# 上記のコメントアウトされた例1を参照してください。
# ここでは、DataMerger の __init__ が horse_processor.preprocessed_data (過去成績) と
# horse_processor._horse_info_data (基本情報) を参照することを期待していると仮定し、
# コンストラクタでファイルパスを指定して初期化するのが最も素直です。
# (もしファイルがない場合は、ダミーデータで試すか、HorseProcessorのデータ取得メソッドを先に実行してください)

# ダミーのファイルパス（実際にファイルが存在するようにしてください）
dummy_horse_results_path = "dummy_horse_results.pkl"
dummy_horse_info_path = "dummy_horse_info.pkl"
if not os.path.exists(dummy_horse_results_path):
    pd.DataFrame({'horse_id': ['1'], 'date': [pd.to_datetime('2023-01-01')], '着順': [1]}).set_index('horse_id').to_pickle(dummy_horse_results_path)
if not os.path.exists(dummy_horse_info_path):
    pd.DataFrame({'horse_id': ['1'], '生年月日': [pd.to_datetime('2020-01-01')]}).set_index('horse_id').to_pickle(dummy_horse_info_path)

horse_processor = HorseProcessor(
    horse_results_filepath=dummy_horse_results_path, # 実際のパスに置き換えてください
    horse_info_filepath=dummy_horse_info_path        # 実際のパスに置き換えてください
)
logger.info("HorseProcessorのデータ準備完了。")



# Jupyter Notebookのセル

# DataMergerのインスタンスを作成
# target_colsやgroup_colsは必要に応じて指定してください
# (指定しない場合はDataMergerConstantsのデフォルト値が使われます)
data_merger = DataMerger(
    race_processor=race_processor,
    horse_processor=horse_processor
    # target_cols=['着順', '人気', '斤量'], # 例: 集計したい過去成績の列
    # group_cols=['開催', 'race_type', '距離'] # 例: ターゲットエンコーディング時のグループ化列
)
logger.info("DataMergerのインスタンス化完了。")


# Jupyter Notebookのセル

logger.info("DataMergerによるデータマージ処理を開始...")
data_merger.merge()
logger.info("データマージ処理完了。")


# Jupyter Notebookのセル

# マージされたデータを取得
merged_df = data_merger.merged_data

# 結果の確認
if not merged_df.empty:
    logger.info("マージされたデータの先頭5行:")
    print(merged_df.head())
    logger.info(f"\nマージされたデータの行数: {len(merged_df)}")
    logger.info(f"マージされたデータのカラム数: {len(merged_df.columns)}")
    logger.info(f"マージされたデータのカラム一覧: {merged_df.columns.tolist()}")

    # 処理概要の取得
    summary = data_merger.get_summary_info()
    logger.info("\n処理概要:")
    for key, value in summary.items():
        if isinstance(value, list) and len(value) > 10: # 長いリストは省略表示
            logger.info(f"  {key}: (最初の10件) {value[:10]}...")
        else:
            logger.info(f"  {key}: {value}")
else:
    logger.warning("マージ処理の結果、データは空でした。")
    logger.warning("RaceProcessor や HorseProcessor のデータ準備、または DataMerger の __init__ でのデータ取得部分を確認してください。")
    # DataMerger の __init__ での _horse_info の設定がうまくいかない場合、
    # ここで merged_df が期待通りでない可能性があります。
